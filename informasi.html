<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Informasi Desa - Desa Wisata Klabili</title>
    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="css/bootstrap/bootstrap.css" />
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/custom.css" />
    <style>
      @keyframes fly {
        0% { transform: translateX(-50px) rotate(0deg) scale(0.8); opacity: 0; }
        50% { opacity: 1; transform: scale(1.1); }
        100% { transform: translateX(50px) rotate(10deg) scale(0.8); opacity: 0; }
      }
      @keyframes sail {
        0% { transform: translateX(-30px) rotate(0deg) scale(0.9); opacity: 0.8; }
        50% { transform: translateX(30px) rotate(5deg) scale(1.05); opacity: 1; }
        100% { transform: translateX(-30px) rotate(0deg) scale(0.9); opacity: 0.8; }
      }
      @keyframes drive {
        0% { transform: translateX(-40px) scale(0.9); opacity: 0.7; }
        50% { transform: translateX(40px) scale(1.1); opacity: 1; }
        100% { transform: translateX(-40px) scale(0.9); opacity: 0.7; }
      }
      @keyframes float {
        0% { transform: translateY(0px) rotate(0deg); }
        50% { transform: translateY(-10px) rotate(5deg); }
        100% { transform: translateY(0px) rotate(0deg); }
      }
      @keyframes fadeInUp {
        0% { opacity: 0; transform: translateY(30px); }
        100% { opacity: 1; transform: translateY(0); }
      }
      .animated-plane {
        animation: fly 3s ease-in-out infinite;
        filter: drop-shadow(2px 4px 3px rgba(0,0,0,0.3));
      }
      .animated-boat {
        animation: sail 4s ease-in-out infinite;
        filter: drop-shadow(1px 3px 2px rgba(0,0,0,0.2));
      }
      .animated-car {
        animation: drive 2s ease-in-out infinite;
        filter: drop-shadow(2px 3px 2px rgba(0,0,0,0.25));
      }
      .transport-icon {
        font-size: 2rem;
        transition: transform 0.3s ease;
        cursor: pointer;
      }
      .transport-icon:hover {
        transform: scale(1.3) rotate(10deg);
        color: #28a745;
      }
      .cloud, .road-1, .road-2, .water {
        transition: transform 0.8s ease, opacity 0.8s ease;
      }
      .cloud {
        transform: translateX(-100px);
        opacity: 0;
      }
      .road-1, .road-2 {
        transform: translateX(100px);
        opacity: 0;
      }
      .water {
        transform: translateX(-100px);
        opacity: 0;
      }
      .transport-section {
        margin-bottom: 20px;
      }

      /* Enhanced Layout Styles */
      .section-divider {
        position: relative;
        height: 120px;
        overflow: hidden;
        background: linear-gradient(45deg, #f8f9fa 25%, transparent 25%),
                    linear-gradient(-45deg, #f8f9fa 25%, transparent 25%),
                    linear-gradient(45deg, transparent 75%, #f8f9fa 75%),
                    linear-gradient(-45deg, transparent 75%, #f8f9fa 75%);
        background-size: 20px 20px;
        background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
        margin: 80px 0;
      }

      .section-divider::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 60px;
        height: 60px;
        background: linear-gradient(45deg, #007bff, #28a745, #ffc107);
        border-radius: 50%;
        opacity: 0.1;
        animation: float 4s ease-in-out infinite;
      }

      .floating-shapes {
        position: absolute;
        width: 100%;
        height: 100%;
        pointer-events: none;
        overflow: hidden;
      }

      .shape {
        position: absolute;
        opacity: 0.08;
        animation: float 6s ease-in-out infinite;
      }

      .shape.circle {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        background: linear-gradient(45deg, #007bff, #28a745);
      }

      .shape.triangle {
        width: 0;
        height: 0;
        border-left: 40px solid transparent;
        border-right: 40px solid transparent;
        border-bottom: 69px solid #ffc107;
      }

      .shape.square {
        width: 50px;
        height: 50px;
        background: linear-gradient(45deg, #dc3545, #fd7e14);
        transform: rotate(45deg);
      }

      .hero-pattern {
        background-image:
          radial-gradient(circle at 25% 25%, rgba(0, 123, 255, 0.08) 0%, transparent 50%),
          radial-gradient(circle at 75% 75%, rgba(40, 167, 69, 0.08) 0%, transparent 50%);
        background-size: 120px 120px;
      }

      .content-section {
        position: relative;
        padding: 100px 0;
        background-attachment: fixed;
        background-size: cover;
      }

      .content-section:nth-child(even) {
        background: linear-gradient(135deg, rgba(248, 249, 250, 0.95) 0%, rgba(233, 236, 239, 0.95) 100%);
      }

      .content-section:nth-child(odd) {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 249, 250, 0.98) 100%);
      }

      .section-title {
        position: relative;
        display: inline-block;
        margin-bottom: 30px;
        font-weight: 800;
        color: #2c3e50;
      }

      .section-title::after {
        content: '';
        position: absolute;
        bottom: -15px;
        left: 50%;
        transform: translateX(-50%);
        width: 100px;
        height: 6px;
        background: linear-gradient(90deg, #007bff, #28a745, #ffc107, #dc3545);
        border-radius: 3px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      }

      .section-subtitle {
        font-size: 1.2rem;
        color: #6c757d;
        margin-bottom: 50px;
        font-weight: 400;
      }

      .card-hover-lift {
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        border: none;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
      }

      .card-hover-lift:hover {
        transform: translateY(-10px) scale(1.02);
        box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
      }

      .decorative-line {
        position: relative;
        margin: 60px 0;
        height: 3px;
        background: linear-gradient(90deg, transparent, #007bff, #28a745, #ffc107, #dc3545, transparent);
        border-radius: 2px;
      }

      .decorative-line::before {
        content: '❋';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: white;
        padding: 0 15px;
        color: #6c757d;
        font-size: 18px;
        font-weight: bold;
      }

      .info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 40px;
        margin: 50px 0;
      }

      .info-card {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 249, 250, 0.95));
        border: none;
        border-radius: 25px;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.08);
        transition: all 0.4s ease;
        backdrop-filter: blur(15px);
        position: relative;
        overflow: hidden;
        padding: 30px;
      }

      .info-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 6px;
        background: linear-gradient(90deg, #007bff, #28a745, #ffc107, #dc3545);
        border-radius: 25px 25px 0 0;
      }

      .info-card:hover {
        transform: translateY(-8px);
        box-shadow: 0 30px 60px rgba(0, 0, 0, 0.12);
      }

      .fade-in-section {
        opacity: 0;
        transform: translateY(40px);
        transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
      }

      .fade-in-section.animate {
        opacity: 1;
        transform: translateY(0);
      }

      .animated-title {
        animation: pulse 3s infinite;
        font-weight: 800;
        text-shadow: 0 2px 4px rgba(0,0,0,0.1);
      }

      .icon-circle {
        width: 100px;
        height: 100px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 25px;
        font-size: 2.5rem;
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        transition: all 0.3s ease;
      }

      .icon-circle:hover {
        transform: scale(1.1) rotate(5deg);
        box-shadow: 0 12px 35px rgba(0,0,0,0.2);
      }

      .contact-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 30px;
        margin: 40px 0;
      }

      .tips-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
        gap: 40px;
        margin: 50px 0;
      }

      .tips-card {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 249, 250, 0.95));
        border: none;
        border-radius: 20px;
        padding: 30px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
      }

      .tips-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 5px;
        background: linear-gradient(90deg, #007bff, #28a745);
      }

      .tips-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12);
      }

      .tips-list {
        list-style: none;
        padding: 0;
      }

      .tips-list li {
        padding: 12px 0;
        border-bottom: 1px solid rgba(0,0,0,0.05);
        display: flex;
        align-items: center;
        transition: all 0.3s ease;
      }

      .tips-list li:hover {
        padding-left: 10px;
        background: rgba(0,123,255,0.02);
      }

      .tips-list li:last-child {
        border-bottom: none;
      }

      .tips-icon {
        margin-right: 15px;
        color: #28a745;
        font-size: 1.2rem;
        min-width: 20px;
      }
    </style>
  </head>
  <body>
    <nav class="navbar navbar-expand-lg bg-body-tertiary">
      <div class="container-fluid">
        <!-- Left side: Logo -->
        <div class="navbar-brand-container" style="flex: 1">
          <a class="navbar-brand" href="index.html"><img src="img/Logo_Desa_Wisata_Klabili-removebg-preview.png" alt=""
              width="100px" /></a>
        </div>

        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarSupportedContent"
          aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
          <span class="navbar-toggler-icon"></span>
        </button>

        
        <!-- Language Toggle Switch - Outside collapsible menu -->
        <div class="navbar-nav d-flex align-items-center">
          <div class="nav-item d-flex align-items-center language-toggle-container id-active"
            id="languageToggleContainer">
            <svg class="flag-img flag-en inactive" width="52" height="36" viewBox="0 0 20 15"
              xmlns="http://www.w3.org/2000/svg" onclick="setLanguage('en')">
              <rect width="20" height="15" fill="#012169" />
              <path d="M0 0L20 15M20 0L0 15" stroke="#fff" stroke-width="2" />
              <path d="M0 0L20 15M20 0L0 15" stroke="#C8102E" stroke-width="1" />
              <path d="M10 0V15M0 7.5H20" stroke="#fff" stroke-width="3" />
              <path d="M10 0V15M0 7.5H20" stroke="#C8102E" stroke-width="2" />
            </svg>
            <span class="language-toggle-dot"></span>
            <svg class="flag-img flag-id active" width="32" height="22" viewBox="0 0 20 15"
              xmlns="http://www.w3.org/2000/svg" onclick="setLanguage('id')">
              <rect width="20" height="7.5" fill="#FF0000" />
              <rect y="7.5" width="20" height="7.5" fill="#FFFFFF" />
            </svg>
          </div>
        </div>

        <div class="collapse navbar-collapse" id="navbarSupportedContent">
          <!-- Center: Navigation items -->
          <ul class="navbar-nav mx-auto mb-2 mb-lg-0 justify-content-center" style="flex: 1">
            <li class="nav-item">
              <button class="btn nav-btn" type="button" data-nav-url="index.html">
                Beranda
              </button>
            </li>

            <li class="nav-item dropdown">
              <button class="btn nav-btn dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                Tentang Kami
              </button>
              <ul class="dropdown-menu">
                <li>
                  <button class="dropdown-item" type="button" data-nav-url="tentang_kami.html#profil-desa">
                    Profil Desa
                  </button>
                </li>
                <li>
                  <button class="dropdown-item" type="button" data-nav-url="tentang_kami.html#struktur-desa">
                    Struktur Desa
                  </button>
                </li>
              </ul>
            </li>

            <li class="nav-item dropdown">
              <button class="btn nav-btn dropdown-toggle" type="button" data-bs-toggle="dropdown"
                aria-expanded="false">
                Fasilitas
              </button>
              <ul class="dropdown-menu">
                <li>
                  <button class="dropdown-item" type="button" data-nav-url="fasilitas.html#fasilitas-umum-desa">
                    Fasilitas Umum Desa
                  </button>
                </li>
              </ul>
            </li>

            <li class="nav-item dropdown">
              <button class="btn nav-btn dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                Wisata & Budaya
              </button>
              <ul class="dropdown-menu">
                <li>
                  <button class="dropdown-item" type="button" data-nav-url="wisata_budaya.html#spot-wisata">
                    Spot Wisata
                  </button>
                </li>
                <li>
                  <button class="dropdown-item" type="button" data-nav-url="wisata_budaya.html#budaya-lokal">
                    Budaya Lokal
                  </button>
                </li>
                <li>
                  <button class="dropdown-item" type="button" data-nav-url="wisata_budaya.html#acara-tahunan">
                    Acara Tahunan
                  </button>
                </li>
                <li>
                  <button class="dropdown-item" type="button" data-nav-url="wisata_budaya.html#kerajinan-tangan">
                    Kerajinan Tangan
                  </button>
                </li>
                <li>
                  <button class="dropdown-item" type="button" data-nav-url="wisata_budaya.html#kuliner-lokal">
                    Kuliner Lokal
                  </button>
                </li>
                <li>
                  <hr class="dropdown-divider" />
                </li>
                <li>
                  <button class="dropdown-item" type="button" data-nav-url="wisata_budaya.html#souvenir">
                    Suvenir
                  </button>
                </li>
              </ul>
            </li>

            <li class="nav-item dropdown">
              <button class="btn nav-btn dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                Informasi Desa
              </button>
              <ul class="dropdown-menu">
                <li>
                  <button class="dropdown-item" type="button" data-nav-url="informasi.html#lokasi-peta">
                    Lokasi & Peta
                  </button>
                </li>
                <li>
                  <button class="dropdown-item" type="button" data-nav-url="informasi.html#kontak">
                    Kontak
                  </button>
                </li>
              </ul>
            </li>

            <li class="nav-item dropdown">
              <button class="btn nav-btn dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                Galeri
              </button>
              <ul class="dropdown-menu">
                <li>
                  <button class="dropdown-item" type="button" data-nav-url="galeri.html#dokumentasi-acara">
                    Dokumentasi Acara
                  </button>
                </li>
                <li>
                  <button class="dropdown-item" type="button" data-nav-url="galeri.html#kegiatan-masyarakat">
                    Kegiatan Masyarakat
                  </button>
                </li>
              </ul>
            </li>
          </ul>
        </div>
      </div>
    </nav>

    <!-- Page Title -->
     <div class="container-fluid bg-primary text-white py-5">
      <div class="container">
        <div class="row">
          <div class="col-12 text-center">
            <h1 class="display-4 fw-bold mb-3 text-white">Informasi Desa Klabili</h1>
            <p class="lead">Panduan lengkap informasi dan lokasi Desa Klabili</p>
        </div>
      </div>
    </div>
  </div>

    <!-- Page Content -->
        <!-- Lokasi & Peta Section -->
        <section id="lokasi-peta" class="py-5" style="background: linear-gradient(135deg, #f0f8f0 0%, #ffffff 100%);">
            <div class="row">
                <div class="col-12 text-center mb-5">
                    <h2 class="display-5 fw-bold" style="color: #2d5a2d;">📍 Lokasi Desa Klabili</h2>
                    <p class="lead" style="color: #6c757d;">Temukan lokasi dan cara menuju desa wisata kami</p>
                </div>
            </div>

            <div class="container">

            <div class="row g-4 align-items-center">
                <!-- Kolom Kiri: Alamat Lengkap -->
                <div class="col-lg-6">
                    <div class="card border-0 shadow-sm" style="background: #ffffff; border-radius: 15px; position: relative; overflow: hidden;">
                        <!-- Ilustrasi Pojok -->
                        <div style="position: absolute; top: 10px; right: 10px; opacity: 0.1;">
                            <i class="fas fa-mountain" style="font-size: 2rem; color: #28a745;"></i>
                        </div>
                        <div style="position: absolute; bottom: 10px; left: 10px; opacity: 0.1;">
                            <i class="fas fa-tree" style="font-size: 1.5rem; color: #20c997;"></i>
                        </div>
                        <div class="card-body p-4">
                            <h5 class="card-title mb-4" style="color: #28a745; font-size: 1.8rem; font-weight: 700;">Alamat Lengkap</h5>
                            <div class="mb-3">
                                <span style="font-size: 1.5rem; font-weight: 800; color: #155724;">Kampung Klabili</span>
                            </div>
                            <div class="mb-2" style="color: #6c757d;">Distrik Selemkai</div>
                            <div class="mb-2" style="color: #6c757d;">Kabupaten Tambrauw</div>
                            <div class="mb-3" style="color: #6c757d;">Provinsi Papua Barat, Indonesia</div>
                            <div class="gps-box" style="background: #d4edda; border: 1px solid #c3e6cb; border-radius: 8px; padding: 10px; display: inline-block;">
                                <i class="fas fa-map-pin" style="color: #28a745;"></i> GPS: -0.7893, 132.0685
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Kolom Kanan: Peta Interaktif -->
                <div class="col-lg-6">
                    <div class="card border-0 shadow-sm" style="background: #ffffff; border-radius: 15px; position: relative; overflow: hidden;">
                        <!-- Ilustrasi Pojok -->
                        <div style="position: absolute; top: 10px; left: 10px; opacity: 0.1;">
                            <i class="fas fa-home" style="font-size: 1.8rem; color: #ffc107;"></i>
                        </div>
                        <div class="card-body p-4">
                            <h5 class="card-title mb-4" style="color: #28a745; font-weight: 700;">Peta Interaktif</h5>
                            <div style="height: 300px; background: #f8f9fa; border-radius: 10px; display: flex; align-items: center; justify-content: center;">
                                <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1m12!1m3!1d3989.123456789012!2d132.0685!3d-0.7893!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x0!2zMMKwNDcnMjEuNSJTIDEzMsKwMDQnMDYuNiJF!5e0!3m2!1sen!2sid!4v1234567890123!5m2!1sen!2sid" width="100%" height="100%" style="border:0; border-radius: 10px;" allowfullscreen="" loading="lazy"></iframe>
                            </div>
                            <div class="text-center mt-3">
                                <a href="https://maps.google.com/?q=-0.7605856254605924, 131.77974998332832" target="_blank" class="btn btn-success">Lihat di Google Maps</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Cara ke Sini -->
            <div class="row mt-5">
                <div class="col-12">
                    <h3 class="text-center mb-4" style="color: #2d5a2d;">🗺️ Cara ke Sini</h3>
                    <!-- Tabs -->
                    <div class="card border-0 shadow-sm" style="background: #ffffff; border-radius: 15px;">
                        <div class="card-body p-4">
                            <ul class="nav nav-tabs" id="transportTabs" role="tablist">
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link active" id="udara-tab" data-bs-toggle="tab" data-bs-target="#udara" type="button" role="tab" aria-controls="udara" aria-selected="true">Via Udara</button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="darat-tab" data-bs-toggle="tab" data-bs-target="#darat" type="button" role="tab" aria-controls="darat" aria-selected="false">Via Darat</button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="laut-tab" data-bs-toggle="tab" data-bs-target="#laut" type="button" role="tab" aria-controls="laut" aria-selected="false">Via Laut</button>
                                </li>
                            </ul>
                            <div class="tab-content mt-3" id="transportTabsContent">
                                <div class="tab-pane fade show active" id="udara" role="tabpanel" aria-labelledby="udara-tab">
                                    <p><strong>Dari Sorong:</strong> Penerbangan ke Bandara Domine Eduard Osok, lalu darat ke Klabili via Sausapor (~4 jam).</p>
                                </div>
                                <div class="tab-pane fade" id="darat" role="tabpanel" aria-labelledby="darat-tab">
                                    <p><strong>Dari Sorong:</strong> Rute Sorong → Sausapor → Klabili dengan mobil 4×4 atau motor trail (~2–4 jam).</p>
                                </div>
                                <div class="tab-pane fade" id="laut" role="tabpanel" aria-labelledby="laut-tab">
                                    <p>Tidak ada feri langsung. Pelabuhan terdekat Sausapor (kargo mingguan), speedboat Sorong–Sausapor (~2 jam) dalam perencanaan.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Rekomendasi Kendaraan -->
            <div class="row mt-4">
                <div class="col-12">
                    <h4 class="text-center mb-3" style="color: #2d5a2d;">🚗 Rekomendasi Kendaraan</h4>
                    <div class="card border-0 shadow-sm" style="background: #ffffff; border-radius: 15px;">
                        <div class="card-body p-4 text-center">
                            <p style="color: #6c757d;">Gunakan mobil double cabin 4×4 (Toyota Hilux, Mitsubishi Pajero) atau motor trail untuk jalan berbatu/lumpur.</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Kontak Section -->
        <section id="kontak" class="py-5 bg-light">
            <div class="container">
                <div class="row">
                    <div class="col-12 text-center mb-5">
                        <h2 class="display-5 fw-bold text-primary">Kontak</h2>
                        <p class="lead text-muted">Hubungi kami untuk informasi dan reservasi</p>
                    </div>
                </div>
                <div class="row g-4 justify-content-center">
                    <div class="col-lg-4 col-md-6 d-flex justify-content-center">
                        <div class="card border-0 shadow-lg h-100" style="background: linear-gradient(135deg, #cce5ff, #99d6ff); border-radius: 20px; box-shadow: 0 8px 16px rgba(0, 123, 255, 0.25); transition: transform 0.3s ease;" onmouseover="this.style.transform='translateY(-5px)'" onmouseout="this.style.transform='translateY(0)'">
                            <div class="card-body text-center p-4">
                                <div class="bg-primary rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center" style="width: 80px; height: 80px; box-shadow: 0 4px 8px rgba(0,123,255,0.3);">
                                    <i class="fas fa-phone text-white fa-2x"></i>
                                </div>
                                <h5 class="card-title" style="color: #004085; font-weight: 700;">Telepon</h5>
                                <p class="card-text" style="color: #004085;">
                                    <strong>Kantor Desa:</strong><br>
                                    +62 123-1234<br><br>
                                    <strong>Koordinator Wisata:</strong><br>
                                    +62 987-6543
                                </p>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-4 col-md-6 d-flex justify-content-center">
                        <div class="card border-0 shadow-lg h-100" style="background: linear-gradient(135deg, #d4edda, #a8d5a3); border-radius: 20px; box-shadow: 0 8px 16px rgba(40, 167, 69, 0.25); transition: transform 0.3s ease;" onmouseover="this.style.transform='translateY(-5px)'" onmouseout="this.style.transform='translateY(0)'">
                            <div class="card-body text-center p-4">
                                <div class="bg-success rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center" style="width: 80px; height: 80px; box-shadow: 0 4px 8px rgba(40,167,69,0.3);">
                                    <i class="fas fa-envelope text-white fa-2x"></i>
                                </div>
                                <h5 class="card-title" style="color: #155724; font-weight: 700;">Email</h5>
                                <p class="card-text" style="color: #155724;">
                                    <strong>Informasi Umum:</strong><br>
                                    <EMAIL><br><br>
                                    <strong>Reservasi:</strong><br>
                                    <EMAIL>
                                </p>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-4 col-md-6 d-flex justify-content-center">
                        <div class="card border-0 shadow-lg h-100" style="background: linear-gradient(135deg, #fff3cd, #ffeaa7); border-radius: 20px; box-shadow: 0 8px 16px rgba(255, 193, 7, 0.25); transition: transform 0.3s ease;" onmouseover="this.style.transform='translateY(-5px)'" onmouseout="this.style.transform='translateY(0)'">
                            <div class="card-body text-center p-4">
                                <div class="bg-warning rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center" style="width: 80px; height: 80px; box-shadow: 0 4px 8px rgba(255,193,7,0.3);">
                                    <i class="fab fa-whatsapp text-white fa-2x"></i>
                                </div>
                                <h5 class="card-title" style="color: #856404; font-weight: 700;">WhatsApp</h5>
                                <p class="card-text" style="color: #856404;">
                                    <strong>Chat Langsung:</strong><br>
                                    +62 82236955445<br><br>
                                    <small class="text-muted">Tersedia 24/7 untuk informasi cepat</small>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row mt-5 justify-content-center">
                    <div class="col-lg-8 d-flex justify-content-center">
                        <div class="card border-0 shadow-lg" style="background: linear-gradient(135deg, #e3f2fd, #bbdefb); border-radius: 20px; box-shadow: 0 8px 16px rgba(13, 110, 253, 0.25); transition: transform 0.3s ease;" onmouseover="this.style.transform='translateY(-5px)'" onmouseout="this.style.transform='translateY(0)'">
                            <div class="card-body p-4">
                                <h5 class="card-title text-center mb-4" style="color: #0d6efd; font-weight: 700;"><i class="fas fa-clock text-info me-2"></i>Jam Operasional</h5>
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6 style="color: #0d6efd; font-weight: 600;">Kantor Desa</h6>
                                        <p class="small" style="color: #0d6efd;">
                                            Senin - Jumat: 08:00 - 16:00<br>
                                            Sabtu: 08:00 - 12:00<br>
                                            Minggu: Tutup
                                        </p>
                                    </div>
                                    <div class="col-md-6">
                                        <h6 style="color: #0d6efd; font-weight: 600;">Pusat Informasi Wisata</h6>
                                        <p class="small" style="color: #0d6efd;">
                                            Setiap Hari: 07:00 - 19:00<br>
                                            <em>Termasuk hari libur</em>
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Tambahan Section -->
        <section id="tambahan" class="py-5">
            <div class="row">
                <div class="col-12 text-center mb-5">
                    <h2 class="display-5 fw-bold text-primary">Informasi Tambahan</h2>
                    <p class="lead text-muted">Tips dan informasi penting untuk kunjungan Anda</p>
                </div>
            </div>

            <div class="row g-4 justify-content-center">
                <div class="col-lg-6 d-flex justify-content-center">
                    <div class="card border-0 shadow-lg h-100" style="width: 100%; background: linear-gradient(135deg, #cce5ff, #99d6ff); border-radius: 20px; box-shadow: 0 8px 16px rgba(0, 123, 255, 0.25); transition: transform 0.3s ease;" onmouseover="this.style.transform='translateY(-5px)'" onmouseout="this.style.transform='translateY(0)'">
                        <div class="card-body p-4 text-start">
                            <h5 class="card-title" style="color: #004085; font-weight: 700;"><i class="fas fa-suitcase text-primary me-2"></i>Tips Berkunjung</h5>
                            <ul class="list-unstyled" style="color: #004085;">
                                <li><i class="fas fa-check text-success me-2"></i>Bawa pakaian hangat (suhu 15-25°C)</li>
                                <li><i class="fas fa-check text-success me-2"></i>Gunakan alas kaki yang nyaman untuk trekking</li>
                                <li><i class="fas fa-check text-success me-2"></i>Bawa kamera untuk mengabadikan momen</li>
                                <li><i class="fas fa-check text-success me-2"></i>Siapkan uang tunai (ATM terbatas)</li>
                                <li><i class="fas fa-check text-success me-2"></i>Hormati adat dan budaya lokal</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="col-lg-6 d-flex justify-content-center">
                    <div class="card border-0 shadow-lg h-100" style="width: 100%; background: linear-gradient(135deg, #fff3cd, #ffeaa7); border-radius: 20px; box-shadow: 0 8px 16px rgba(255, 193, 7, 0.25); transition: transform 0.3s ease;" onmouseover="this.style.transform='translateY(-5px)'" onmouseout="this.style.transform='translateY(0)'">
                        <div class="card-body p-4 text-start">
                            <h5 class="card-title" style="color: #856404; font-weight: 700;"><i class="fas fa-exclamation-triangle text-warning me-2"></i>Hal Penting</h5>
                            <ul class="list-unstyled" style="color: #856404;">
                                <li><i class="fas fa-info-circle text-info me-2"></i>Reservasi homestay minimal 3 hari sebelumnya</li>
                                <li><i class="fas fa-info-circle text-info me-2"></i>Musim terbaik: April-Oktober (kemarau)</li>
                                <li><i class="fas fa-info-circle text-info me-2"></i>Sinyal telepon terbatas di beberapa area</li>
                                <li><i class="fas fa-info-circle text-info me-2"></i>Jaga kebersihan dan kelestarian alam</li>
                                <li><i class="fas fa-info-circle text-info me-2"></i>Ikuti panduan dari pemandu lokal</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <!-- Footer -->
  <footer class="bg-dark text-white py-5">
    <div class="container">
      <div class="row">
        <!-- Column 1: Logo and Description -->
        <div class="col-lg-3 col-md-6 mb-4">
          <div class="mb-3">
            <img src="img/Logo_Desa_Wisata_Klabili-removebg-preview.png" alt="Logo Desa Wisata Klabili" width="150px" />
          </div>
          <p class="text-white-50">
            Desa Wisata Klabili menawarkan pengalaman wisata autentik dengan keindahan alam, budaya lokal, dan keramahan masyarakat Suku Moi.
          </p>
        </div>

        <!-- Column 2: Quick Links -->
        <div class="col-lg-3 col-md-6 mb-4">
          <h5 class="fw-bold mb-3">Tautan Cepat</h5>
          <ul class="list-unstyled">
            <li class="mb-2"><a href="index.html" class="text-white-50 text-decoration-none">Beranda</a></li>
            <li class="mb-2"><a href="tentang_kami.html" class="text-white-50 text-decoration-none">Tentang Kami</a></li>
            <li class="mb-2"><a href="fasilitas.html" class="text-white-50 text-decoration-none">Fasilitas</a></li>
            <li class="mb-2"><a href="wisata_budaya.html" class="text-white-50 text-decoration-none">Wisata & Budaya</a></li>
            <li class="mb-2"><a href="galeri.html" class="text-white-50 text-decoration-none">Galeri</a></li>
          </ul>
        </div>

        <!-- Column 3: Contact Info -->
        <div class="col-lg-3 col-md-6 mb-4">
          <h5 class="fw-bold mb-3">Kontak Kami</h5>
          <ul class="list-unstyled">
            <li class="mb-2">
              <i class="fas fa-phone me-2"></i>
              <span>+62 822-3695-5445</span>
            </li>
            <li class="mb-2">
              <i class="fas fa-map-marker-alt me-2"></i>
              <span>Kampung Klabili, Distrik Selemkai, Kabupaten Tambrauw</span>
            </li>
            <li class="mb-2">
              <i class="fas fa-envelope me-2"></i>
              <span><EMAIL></span>
             </li>
          </ul>
        </div>

        <!-- Column 4: Social Media -->
        <div class="col-lg-3 col-md-6 mb-4">
          <h5 class="fw-bold mb-3">Ikuti Kami</h5>
          <div class="d-flex">
            <a href="https://www.instagram.com/klabili_bridwhtcing/" target="_blank" rel="noopener noreferrer"
              class="socialContainer containerOne me-3">
              <svg class="socialSvg instagramSvg" viewBox="0 0 16 16">
                <path
                  d="M8 0C5.829 0 5.556.01 4.703.048 3.85.088 3.269.222 2.76.42a3.917 3.917 0 0 0-1.417.923A3.927 3.927 0 0 0 .42 2.76C.222 3.268.087 3.85.048 4.7.01 5.555 0 5.827 0 8.001c0 2.172.01 2.444.048 3.297.04.852.174 1.433.372 1.942.205.526.478.972.923 1.417.444.445.89.719 1.416.923.51.198 1.09.333 1.942.372C5.555 15.99 5.827 16 8 16s2.444-.01 3.298-.048c.851-.04 1.434-.174 1.943-.372a3.916 3.916 0 0 0 1.416-.923c.445-.445.718-.891.923-1.417.197-.509.332-1.09.372-1.942C15.99 10.445 16 10.173 16 8s-.01-2.445-.048-3.299c-.04-.851-.175-1.433-.372-1.941a3.926 3.926 0 0 0-.923-1.417A3.911 3.911 0 0 0 13.24.42c-.51-.198-1.092-.333-1.943-.372C10.443.01 10.172 0 7.998 0h.003zm-.717 1.442h.718c2.136 0 2.389.007 3.232.046.78.035 1.204.166 1.486.275.373.145.64.319.92.599.28.28.453.546.598.92.11.281.24.705.275 1.485.039.843.047 1.096.047 3.231s-.008 2.389-.047 3.232c-.035.78-.166 1.203-.275 1.485a2.47 2.47 0 0 1-.599.919c-.28.28-.546.453-.92.598-.28.11-.704.24-1.485.276-.843.038-1.096.047-3.232.047s-2.39-.009-3.233-.047c-.78-.036-1.203-.166-1.485-.276a2.478 2.478 0 0 1-.92-.598 2.48 2.48 0 0 1-.6-.92c-.109-.281-.24-.705-.275-1.485-.038-.843-.046-1.096-.046-3.233 0-2.136.008-2.388.046-3.231.036-.78.166-1.204.276-1.486.145-.373.319-.64.599-.92.28-.28.546-.453.92-.598.282-.11.705-.24 1.485-.276.738-.034 1.024-.044 2.515-.045v.002zm4.988 1.328a.96.96 0 1 0 0 1.92.96.96 0 0 0 0-1.92zm-4.27 1.122a4.109 4.109 0 1 0 0 8.217 4.109 4.109 0 0 0 0-8.217zm0 1.441a2.667 2.667 0 1 1 0 5.334 2.667 2.667 0 0 1 0-5.334z">
                </path>
              </svg>
            </a>
            <a href="https://www.facebook.com/profile.php?id=100094418988540" target="_blank" rel="noopener noreferrer"
              class="socialContainer containerTwo me-3">
              <svg class="socialSvg facebookSvg" viewBox="0 0 16 16">
                <path
                  d="M15.117 0H.883A.884.884 0 0 0 0 .883v14.234C0 15.548.396 16 .883 16h7.675v-6.176H6.548V7.41h2.01V5.797c0-1.99 1.216-3.078 2.993-3.078.868 0 1.617.064 1.836.093v2.13h-1.26c-.988 0-1.18.47-1.18 1.158V7.41h2.36l-.308 2.414h-2.052V16h4.025A.884.884 0 0 0 16 15.117V.883A.884.884 0 0 0 15.117 0z">
                </path>
              </svg>
            </a>
            <a href="https://wa.me/6282236955445?text=Halo%20saya%20mau%20tanya" target="_blank"
              rel="noopener noreferrer" class="socialContainer
              containerFour">
              <svg class="socialSvg whatsappSvg" viewBox="0 0 16 16">
                <path
                  d="M13.601 2.326A7.854 7.854 0 0 0 7.994 0C3.627 0 .068 3.558.064 7.926c0 1.399.366 2.76 1.057 3.965L0 16l4.204-1.102a7.933 7.933 0 0 0 3.79.965h.004c4.368 0 7.926-3.558 7.93-7.93A7.898 7.898 0 0 0 13.6 2.326zM7.994 14.521a6.573 6.573 0 0 1-3.356-.92l-.24-.144-2.494.654.666-2.433-.156-.251a6.56 6.56 0 0 1-1.007-3.505c0-3.626 2.957-6.584 6.591-6.584a6.56 6.56 0 0 1 4.66 1.931 6.557 6.557 0 0 1 1.928 4.66c-.004 3.639-2.961 6.592-6.592 6.592zm3.615-4.934c-.197-.099-1.17-.578-1.353-.646-.182-.065-.315-.099-.445.099-.133.197-.513.646-.627.775-.114.133-.232.148-.43.05-.197-.1-.836-.308-1.592-.985-.59-.525-.985-1.175-1.103-1.372-.114-.198-.011-.304.088-.403.087-.088.197-.232.296-.346.1-.114.133-.198.198-.33.065-.134.034-.248-.015-.347-.05-.099-.445-1.076-.612-1.47-.16-.389-.323-.335-.445-.34-.114-.007-.247-.007-.38-.007a.729.729 0 0 0-.529.247c-.182.198-.691.677-.691 1.654 0 .977.71 1.916.81 2.049.098.133 1.394 2.132 3.383 2.992.47.205.84.326 1.129.418.475.152.904.129 1.246.08.38-.058 1.171-.48 1.338-.943.164-.464.164-.86.114-.943-.049-.084-.182-.133-.38-.232z">
                </path>
              </svg>
            </a>
          </div>
        </div>
      </div>

      <!-- Copyright -->
      <hr class="my-4">
      <div class="row">
        <div class="col-12 text-center">
          <p class="mb-0 text-white-50">
            &copy; 2025 Desa Wisata Klabili. Semua hak dilindungi.
          </p>
        </div>
      </div>
    </div>
  </footer>

<!-- Scroll to Top Button -->
<button id="scrollToTopBtn" class="btn btn-success rounded-circle shadow" style="position: fixed; bottom: 32px; right: 32px; z-index: 999; display: none; width: 48px; height: 48px; align-items: center; justify-content: center;">
  <i class="fas fa-leaf"></i>
</button>
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" />

    <!-- Bootstrap JS -->
    <script src="js/bootstrap/bootstrap.bundle.js"></script>
    <!-- Custom JS -->
    <script src="js/custom.js"></script>

    <!-- Scroll Animation Script -->
    <script>
      document.addEventListener('DOMContentLoaded', function() {
        const observerOptions = {
          threshold: 0.1,
          rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
          entries.forEach(entry => {
            if (entry.isIntersecting) {
              const element = entry.target;
              element.style.opacity = '1';
              element.style.transform = 'translateY(0) translateX(0)';

              // Special animations for transport sections
              if (element.id === 'via-udara') {
                setTimeout(() => {
                  const plane = element.querySelector('.animated-plane');
                  plane.style.left = '0px';
                  setTimeout(() => {
                    const clouds = element.querySelectorAll('.cloud');
                    clouds.forEach((cloud, index) => {
                      setTimeout(() => {
                        cloud.style.transform = 'translateX(0)';
                        cloud.style.opacity = '1';
                      }, index * 500);
                    });
                  }, 1000);
                }, 500);
              } else if (element.id === 'via-darat') {
                setTimeout(() => {
                  const car = element.querySelector('.animated-car');
                  car.style.right = '0px';
                  setTimeout(() => {
                    const roads = element.querySelectorAll('.road-1, .road-2');
                    roads.forEach((road, index) => {
                      setTimeout(() => {
                        road.style.transform = 'translateX(0)';
                        road.style.opacity = '1';
                      }, index * 500);
                    });
                  }, 1000);
                }, 500);
              } else if (element.id === 'via-laut') {
                setTimeout(() => {
                  const boat = element.querySelector('.animated-boat');
                  boat.style.left = '0px';
                  setTimeout(() => {
                    const water = element.querySelector('.water');
                    water.style.transform = 'translateX(0)';
                    water.style.opacity = '1';
                  }, 1000);
                }, 500);
              } else if (element.id === 'rekomendasi-transportasi') {
                setTimeout(() => {
                  document.getElementById('rekomendasi-transportasi-hr').style.opacity = '1';
                  document.getElementById('rekomendasi-transportasi-hr').style.transform = 'translateY(0)';
                  setTimeout(() => {
                    document.getElementById('rekomendasi-transportasi-text').style.opacity = '1';
                    document.getElementById('rekomendasi-transportasi-text').style.transform = 'translateY(0)';
                  }, 300);
                }, 500);
              }
            }
          });
        }, observerOptions);

        // Observe elements
        const elementsToObserve = [
          'akses-transportasi-title',
          'via-udara',
          'via-darat',
          'via-laut',
          'rekomendasi-transportasi'
        ];

        elementsToObserve.forEach(id => {
          const element = document.getElementById(id);
          if (element) {
            observer.observe(element);
          }
        });

        // Initial styles are now handled by CSS
      });
    </script>
  </body>
</html>
